<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Selected state -->
    <item android:state_selected="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:type="linear"
                        android:angle="135"
                        android:startColor="@color/enhanced_picker_gradient_start"
                        android:endColor="@color/enhanced_picker_gradient_end" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
            <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:type="linear"
                        android:angle="90"
                        android:startColor="#20FFFFFF"
                        android:endColor="#00FFFFFF" />
                    <corners android:radius="11dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Default state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/enhanced_picker_icon_background" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/enhanced_picker_border" />
        </shape>
    </item>
    
</selector>
