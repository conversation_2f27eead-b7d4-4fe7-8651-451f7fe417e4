<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:type="linear"
                        android:angle="135"
                        android:startColor="@color/enhanced_picker_gradient_start"
                        android:endColor="@color/enhanced_picker_gradient_end" />
                    <corners android:radius="16dp" />
                </shape>
            </item>
            <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:type="linear"
                        android:angle="90"
                        android:startColor="#30FFFFFF"
                        android:endColor="#10FFFFFF" />
                    <corners android:radius="15dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- Default state -->
    <item>
        <layer-list>
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="@color/enhanced_picker_icon_background" />
                    <corners android:radius="16dp" />
                    <stroke
                        android:width="2dp"
                        android:color="@color/enhanced_picker_gradient_end" />
                </shape>
            </item>
            <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
                <shape android:shape="rectangle">
                    <gradient
                        android:type="linear"
                        android:angle="90"
                        android:startColor="#08FFFFFF"
                        android:endColor="#00FFFFFF" />
                    <corners android:radius="15dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
</selector>
