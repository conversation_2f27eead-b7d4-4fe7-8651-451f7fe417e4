<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="CircleImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="CircleBackground">
        <item name="android:background">@drawable/circle_background</item>
    </style>

    <style name="NavigationDrawerItemShape">
        <item name="cornerSize">8dp</item>
        <item name="cornerFamily">rounded</item>
    </style>

    <!-- Order Card Style -->
    <style name="OrderCardStyle" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">@dimen/card_corner_radius</item>
        <item name="cardElevation">@dimen/elevation_card</item>
        <item name="cardBackgroundColor">@color/white</item>
        <item name="rippleColor">@color/primary_light</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">true</item>
    </style>

    <!-- Order Button Style -->
    <style name="OrderButtonStyle" parent="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textSize">@dimen/text_size_micro</item>
        <item name="android:padding">@dimen/margin_extra_small</item>
        <item name="cornerRadius">@dimen/button_corner_radius</item>
        <item name="android:minHeight">36dp</item>
    </style>

    <!-- Order Track Button Style -->
    <style name="OrderTrackButtonStyle" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:textSize">@dimen/text_size_micro</item>
        <item name="android:padding">@dimen/margin_extra_small</item>
        <item name="cornerRadius">@dimen/button_corner_radius</item>
        <item name="android:minHeight">36dp</item>
    </style>

    <!-- Filter Chip Style -->
    <style name="FilterChipStyle" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="android:textColor">@color/primary</item>
        <item name="chipBackgroundColor">@color/chip_background_selector</item>
        <item name="android:textSize">14sp</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipMinTouchTargetSize">32dp</item>
        <item name="chipStrokeWidth">0dp</item>
    </style>

    <!-- Price Text Style -->
    <style name="PriceTextStyle">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:background">@drawable/price_background</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">6dp</item>
        <item name="android:paddingBottom">6dp</item>
    </style>

    <!-- Add To Cart Button Style -->
    <style name="AddToCartButtonStyle" parent="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@android:color/white</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:minHeight">40dp</item>
        <item name="cornerRadius">20dp</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="elevation">2dp</item>
    </style>

    <!-- Custom Alert Dialog Theme -->
    <style name="AlertDialogTheme" parent="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorSurface">@color/white</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="buttonBarPositiveButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">@style/AlertDialogButtonStyle</item>
        <item name="shapeAppearanceOverlay">@style/AlertDialogCorners</item>
    </style>

    <!-- Alert Dialog Button Style -->
    <style name="AlertDialogButtonStyle" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/primary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <!-- Alert Dialog Corner Style -->
    <style name="AlertDialogCorners">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <!-- Enhanced Date Picker Theme -->
    <style name="ThemeOverlay_App_DatePicker" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="colorPrimary">@color/enhanced_picker_gradient_end</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/enhanced_picker_gradient_start</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSurface">@color/enhanced_picker_card_background</item>
        <item name="colorOnSurface">@color/enhanced_picker_value_text</item>
        <item name="colorSurfaceVariant">@color/enhanced_picker_icon_background</item>
        <item name="colorOnSurfaceVariant">@color/enhanced_picker_label_text</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.App.DatePicker.Enhanced.HeaderTitle</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.App.DatePicker.Enhanced.HeaderSelection</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.App.DatePicker.Enhanced.HeaderToggleButton</item>
        <item name="materialCalendarDay">@style/Widget.App.DatePicker.Enhanced.Day</item>
        <item name="materialCalendarStyle">@style/Widget.App.DatePicker.Enhanced</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.App.DatePicker.Enhanced.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.App.DatePicker.Enhanced.Button</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.App.DatePicker.Enhanced.HeaderDivider</item>
        <item name="materialCalendarMonth">@style/Widget.App.DatePicker.Enhanced.Month</item>
        <item name="materialCalendarYear">@style/Widget.App.DatePicker.Enhanced.Year</item>
        <item name="android:windowBackground">@drawable/bg_enhanced_picker_dialog</item>
        <item name="android:windowAnimationStyle">@style/Animation.App.DatePicker</item>
    </style>

    <!-- Enhanced Date Picker Widget Styles -->
    <style name="Widget.App.DatePicker.Enhanced" parent="Widget.MaterialComponents.MaterialCalendar">
        <item name="android:padding">24dp</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.ExtraLargeComponent</item>
        <item name="android:background">@drawable/bg_enhanced_picker_dialog_content</item>
        <item name="android:elevation">16dp</item>
        <item name="android:layout_margin">16dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.HeaderTitle" parent="Widget.MaterialComponents.MaterialCalendar.HeaderTitle">
        <item name="android:textAppearance">?attr/textAppearanceHeadline5</item>
        <item name="android:textColor">@color/enhanced_picker_gradient_end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.HeaderSelection" parent="Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textColor">@color/enhanced_picker_gradient_end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.02</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:background">@drawable/bg_enhanced_picker_header_selection</item>
        <item name="android:padding">12dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.HeaderToggleButton" parent="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:tint">@color/enhanced_picker_gradient_end</item>
        <item name="backgroundTint">@color/enhanced_picker_icon_background</item>
        <item name="rippleColor">@color/enhanced_picker_ripple_color</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.MediumComponent</item>
        <item name="android:elevation">4dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.HeaderDivider">
        <item name="android:background">@drawable/bg_enhanced_picker_divider</item>
        <item name="android:layout_height">2dp</item>
        <item name="android:layout_marginTop">8dp</item>
        <item name="android:layout_marginBottom">8dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.Day" parent="Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemShapeAppearance">@style/ShapeAppearance.App.MediumComponent</item>
        <item name="itemTextColor">@color/enhanced_picker_label_text</item>
        <item name="itemStrokeColor">@color/enhanced_picker_border</item>
        <item name="itemStrokeWidth">1dp</item>
        <item name="itemFillColor">@color/enhanced_picker_icon_background</item>
        <item name="itemRippleColor">@color/enhanced_picker_ripple_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/roboto_medium</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.Month" parent="Widget.MaterialComponents.MaterialCalendar.Month">
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@drawable/bg_enhanced_picker_month</item>
        <item name="android:elevation">2dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.Year" parent="Widget.MaterialComponents.MaterialCalendar.Year">
        <item name="android:textColor">@color/enhanced_picker_value_text</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:background">@drawable/bg_enhanced_picker_year_item</item>
        <item name="android:layout_margin">4dp</item>
    </style>

    <style name="Widget.App.DatePicker.Enhanced.Button" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/enhanced_picker_gradient_end</item>
        <item name="rippleColor">@color/enhanced_picker_ripple_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.MediumComponent</item>
        <item name="android:background">@drawable/bg_enhanced_picker_button</item>
        <item name="android:minWidth">120dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Enhanced Time Picker Theme -->
    <style name="ThemeOverlay_App_TimePicker" parent="@style/ThemeOverlay.MaterialComponents.TimePicker">
        <item name="colorPrimary">@color/enhanced_picker_gradient_end</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/enhanced_picker_gradient_start</item>
        <item name="colorOnSecondary">@color/white</item>
        <item name="colorSurface">@color/enhanced_picker_card_background</item>
        <item name="colorOnSurface">@color/enhanced_picker_value_text</item>
        <item name="colorSurfaceVariant">@color/enhanced_picker_icon_background</item>
        <item name="colorOnSurfaceVariant">@color/enhanced_picker_label_text</item>
        <item name="materialClockStyle">@style/Widget.App.TimePicker.Enhanced.Clock</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.App.TimePicker.Enhanced.Button</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.App.TimePicker.Enhanced.Button</item>
        <item name="chipStyle">@style/Widget.App.TimePicker.Enhanced.Chip</item>
        <item name="materialTimePickerStyle">@style/Widget.App.TimePicker.Enhanced</item>
        <item name="materialTimePickerTitleStyle">@style/Widget.App.TimePicker.Enhanced.Title</item>
        <item name="android:windowBackground">@drawable/bg_enhanced_picker_dialog</item>
        <item name="android:windowAnimationStyle">@style/Animation.App.TimePicker</item>
    </style>

    <!-- Enhanced Time Picker Widget Styles -->
    <style name="Widget.App.TimePicker.Enhanced" parent="Widget.MaterialComponents.TimePicker">
        <item name="android:padding">24dp</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.ExtraLargeComponent</item>
        <item name="android:background">@drawable/bg_enhanced_picker_dialog_content</item>
        <item name="android:elevation">16dp</item>
        <item name="android:layout_margin">16dp</item>
    </style>

    <style name="Widget.App.TimePicker.Enhanced.Title" parent="Widget.MaterialComponents.TimePicker.Title">
        <item name="android:textAppearance">?attr/textAppearanceHeadline5</item>
        <item name="android:textColor">@color/enhanced_picker_gradient_end</item>
        <item name="android:textStyle">bold</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:layout_marginBottom">16dp</item>
    </style>

    <style name="Widget.App.TimePicker.Enhanced.Clock" parent="Widget.MaterialComponents.TimePicker.Clock">
        <item name="clockFaceBackgroundColor">@drawable/bg_enhanced_picker_clock_face</item>
        <item name="clockHandColor">@color/enhanced_picker_gradient_end</item>
        <item name="clockNumberTextColor">@color/enhanced_picker_label_text</item>
        <item name="android:background">@drawable/bg_enhanced_picker_clock_background</item>
        <item name="android:elevation">8dp</item>
        <item name="android:layout_margin">16dp</item>
    </style>

    <style name="Widget.App.TimePicker.Enhanced.Button" parent="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:textColor">@color/enhanced_picker_gradient_end</item>
        <item name="rippleColor">@color/enhanced_picker_ripple_color</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:letterSpacing">0.03</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.MediumComponent</item>
        <item name="android:background">@drawable/bg_enhanced_picker_button</item>
        <item name="android:minWidth">120dp</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="Widget.App.TimePicker.Enhanced.Chip" parent="Widget.MaterialComponents.TimePicker.Display.TextInputEditText">
        <item name="android:textColor">@color/enhanced_picker_value_text</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:fontFamily">@font/roboto_bold</item>
        <item name="android:background">@drawable/bg_enhanced_picker_chip</item>
        <item name="shapeAppearance">@style/ShapeAppearance.App.MediumComponent</item>
        <item name="android:letterSpacing">0.05</item>
        <item name="android:elevation">4dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:minWidth">80dp</item>
        <item name="android:minHeight">56dp</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- Enhanced Shape Appearances -->
    <style name="ShapeAppearance.App.ExtraLargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">24dp</item>
    </style>

    <style name="ShapeAppearance.App.LargeComponent" parent="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">20dp</item>
    </style>

    <style name="ShapeAppearance.App.MediumComponent" parent="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">16dp</item>
    </style>

    <style name="ShapeAppearance.App.SmallComponent" parent="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">12dp</item>
    </style>

    <!-- Enhanced Picker Animations -->
    <style name="Animation.App.DatePicker" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/enhanced_picker_enter</item>
        <item name="android:windowExitAnimation">@anim/enhanced_picker_exit</item>
    </style>

    <style name="Animation.App.TimePicker" parent="@android:style/Animation.Dialog">
        <item name="android:windowEnterAnimation">@anim/enhanced_picker_enter</item>
        <item name="android:windowExitAnimation">@anim/enhanced_picker_exit</item>
    </style>

    <!-- Promotional Dialog Theme -->
    <style name="PromoDialogTheme" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.7</item>
    </style>

    <!-- Shop Map Styles -->
    <style name="CircularImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="RoundedImageView" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">8dp</item>
    </style>

    <!-- Modern Search Bar Styles -->
    <style name="ModernSearchBarStyle" parent="">
        <item name="android:background">@drawable/modern_search_bar_selector</item>
        <item name="android:elevation">12dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <style name="ModernSearchInputStyle" parent="">
        <item name="android:background">@drawable/modern_search_input_background</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:textColorHint">@color/black</item>
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="ModernSearchIconStyle" parent="">
        <item name="android:tint">@color/search_icon_primary</item>
        <item name="android:scaleType">center</item>
    </style>

    <style name="ModernFilterButtonStyle" parent="">
        <item name="android:background">@drawable/modern_filter_button_background</item>
        <item name="android:tint">@color/black</item>
        <item name="android:scaleType">center</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

    <!-- Collapsing Toolbar Text Styles -->
    <style name="TextAppearance.App.CollapsingToolbar.Expanded" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="TextAppearance.App.CollapsingToolbar.Collapsed" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Transparent Dialog Style -->
    <style name="TransparentDialog" parent="Theme.Material3.DayNight.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>

    <!-- Tab Text Appearance for Shop Details -->
    <style name="TabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="textAllCaps">false</item>
    </style>
</resources>
