<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Month background -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/enhanced_picker_card_background" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/enhanced_picker_border" />
        </shape>
    </item>
    
    <!-- Subtle highlight -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#08FFFFFF"
                android:endColor="#00FFFFFF" />
            <corners android:radius="11dp" />
        </shape>
    </item>
    
</layer-list>
