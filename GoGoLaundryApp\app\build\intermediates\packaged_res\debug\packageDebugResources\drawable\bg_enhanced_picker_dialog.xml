<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Background dim -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#80000000" />
        </shape>
    </item>
    
    <!-- Glassmorphism background -->
    <item android:top="40dp" android:bottom="40dp" android:left="20dp" android:right="20dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="#F0FFFFFF"
                android:centerColor="#E8FFFFFF"
                android:endColor="#F5FFFFFF" />
            <corners android:radius="24dp" />
            <stroke
                android:width="1dp"
                android:color="#30FFFFFF" />
        </shape>
    </item>
    
    <!-- Subtle inner glow -->
    <item android:top="41dp" android:bottom="41dp" android:left="21dp" android:right="21dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="200dp"
                android:centerX="0.5"
                android:centerY="0.3"
                android:startColor="#10667eea"
                android:endColor="#00667eea" />
            <corners android:radius="23dp" />
        </shape>
    </item>
    
</layer-list>
