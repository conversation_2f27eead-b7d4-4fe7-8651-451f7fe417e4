<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Background with gradient -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="135"
                android:startColor="@color/enhanced_picker_gradient_start"
                android:endColor="@color/enhanced_picker_gradient_end" />
            <corners android:radius="16dp" />
        </shape>
    </item>
    
    <!-- Highlight overlay -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <gradient
                android:type="linear"
                android:angle="90"
                android:startColor="#25FFFFFF"
                android:centerColor="#15FFFFFF"
                android:endColor="#00FFFFFF" />
            <corners android:radius="15dp" />
        </shape>
    </item>
    
</layer-list>
