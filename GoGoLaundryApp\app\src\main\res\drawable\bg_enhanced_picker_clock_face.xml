<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Clock face background -->
    <item>
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="120dp"
                android:centerX="0.5"
                android:centerY="0.5"
                android:startColor="@color/enhanced_picker_card_background"
                android:endColor="@color/enhanced_picker_icon_background" />
            <stroke
                android:width="2dp"
                android:color="@color/enhanced_picker_border" />
        </shape>
    </item>
    
    <!-- Inner highlight -->
    <item android:top="4dp" android:left="4dp" android:right="4dp" android:bottom="4dp">
        <shape android:shape="oval">
            <gradient
                android:type="radial"
                android:gradientRadius="100dp"
                android:centerX="0.5"
                android:centerY="0.5"
                android:startColor="#15FFFFFF"
                android:endColor="#00FFFFFF" />
        </shape>
    </item>
    
    <!-- Center dot -->
    <item android:top="50%" android:left="50%" android:right="50%" android:bottom="50%">
        <shape android:shape="oval">
            <solid android:color="@color/enhanced_picker_gradient_end" />
        </shape>
    </item>
    
</layer-list>
