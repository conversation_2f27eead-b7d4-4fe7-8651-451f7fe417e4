{"logs": [{"outputFile": "com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "129,130,131,132,133,134,135,162", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6539,6609,6693,6777,6873,6975,7077,10059", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "6604,6688,6772,6868,6970,7072,7166,10143"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,96,142,187,237,291,342,382,427,472,529,583,641,691,746,792,844,890,937,989,1038,1080,1127,1175,1215,1261,1314,1363,1422,1471,1523,1578,1632,1684,1738,1791,1846,1900,1956,2010,2066,2111,2162,2214,2267,2320,2375,2428,2482,2540,2594,2651,2710,2768,2824,2863,2916,2976,3030,3087,3142,3196,3252,3308,3369,3424,3481,3523,3570,3618,3680,3729,3781,3827,3880,3935,3997,4061,4119,4179,4232,4286,4340,4398,4455,4505,4559,4617,4669,4723,4769,4817,4863,4910,4952,5003,5059,5110,5157,5209,5258,5312,5360,5402,5442,5487,5532,5577,5622", "endColumns": "40,45,44,49,53,50,39,44,44,56,53,57,49,54,45,51,45,46,51,48,41,46,47,39,45,52,48,58,48,51,54,53,51,53,52,54,53,55,53,55,44,50,51,52,52,54,52,53,57,53,56,58,57,55,38,52,59,53,56,54,53,55,55,60,54,56,41,46,47,61,48,51,45,52,54,61,63,57,59,52,53,53,57,56,49,53,57,51,53,45,47,45,46,41,50,55,50,46,51,48,53,47,41,39,44,44,44,44,44", "endOffsets": "91,137,182,232,286,337,377,422,467,524,578,636,686,741,787,839,885,932,984,1033,1075,1122,1170,1210,1256,1309,1358,1417,1466,1518,1573,1627,1679,1733,1786,1841,1895,1951,2005,2061,2106,2157,2209,2262,2315,2370,2423,2477,2535,2589,2646,2705,2763,2819,2858,2911,2971,3025,3082,3137,3191,3247,3303,3364,3419,3476,3518,3565,3613,3675,3724,3776,3822,3875,3930,3992,4056,4114,4174,4227,4281,4335,4393,4450,4500,4554,4612,4664,4718,4764,4812,4858,4905,4947,4998,5054,5105,5152,5204,5253,5307,5355,5397,5437,5482,5527,5572,5617,5662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,163,164,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7171,7246,7357,7446,7547,7654,7761,7860,7967,8070,8197,8285,8409,8511,8613,8729,8831,8945,9073,9189,9311,9447,9567,9701,9821,9933,10148,10265,10389,10519,10641,10779,10913,11029", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "7241,7352,7441,7542,7649,7756,7855,7962,8065,8192,8280,8404,8506,8608,8724,8826,8940,9068,9184,9306,9442,9562,9696,9816,9928,10054,10260,10384,10514,10636,10774,10908,11024,11144"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "19", "endColumns": "12", "endOffsets": "1099"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "5667", "endLines": "128", "endColumns": "12", "endOffsets": "6534"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.1\\com.mdsadrulhasan.gogolaundry.app-mergeDebugResources-52:\\values-night-v8\\values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\b1a457fa5d970673514889d79cab362f\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "129,130,131,132,133,134,135,162", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "6530,6600,6684,6768,6864,6966,7068,10050", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "6595,6679,6763,6859,6961,7063,7157,10134"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "5,6,12,13,136,32,23,145,26,7,9,8,40,137,37,14,28,27,107,108,31,147,146,17,96,55,99,95,56,132,133,131,92,91,88,87,64,63,90,89,25,78,80,81,79,74,75,84,76,77,73,72,83,82,19,69,45,51,46,47,44,43,52,48,49,50,10,3,4,35,144,140,109,124,125,115,116,117,118,128,121,122,123,126,127,119,120,60,59,112,111,110,36,16,67,68,143,29,138,30,139,24,18,22,106,105,104,103,102", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "178,219,566,611,5940,1379,970,6405,1103,265,406,348,1669,5994,1597,661,1195,1148,4573,4625,1337,6498,6450,782,4180,2366,4269,4121,2419,5784,5836,5730,4031,3977,3814,3759,2703,2647,3923,3867,1058,3339,3443,3495,3390,3119,3174,3662,3227,3285,3062,3003,3604,3548,864,2902,1876,2221,1936,1993,1822,1766,2275,2048,2109,2164,493,83,130,1488,6322,6155,4674,5421,5474,4899,4961,5025,5083,5636,5255,5309,5363,5529,5586,5143,5197,2558,2504,4814,4766,4720,1550,740,2795,2846,6271,1241,6049,1288,6101,1010,822,930,4528,4483,4438,4393,4348", "endColumns": "40,45,44,49,53,50,39,44,44,56,53,57,49,54,45,51,45,46,51,48,41,46,47,39,45,52,48,58,48,51,54,53,51,53,52,54,53,55,53,55,44,50,51,52,52,54,52,53,57,53,56,58,57,55,38,52,59,53,56,54,53,55,55,60,54,56,41,46,47,61,48,51,45,52,54,61,63,57,59,52,53,53,57,56,49,53,57,51,53,45,47,45,46,41,50,55,50,46,51,48,53,47,41,39,44,44,44,44,44", "endOffsets": "214,260,606,656,5989,1425,1005,6445,1143,317,455,401,1714,6044,1638,708,1236,1190,4620,4669,1374,6540,6493,817,4221,2414,4313,4175,2463,5831,5886,5779,4078,4026,3862,3809,2752,2698,3972,3918,1098,3385,3490,3543,3438,3169,3222,3711,3280,3334,3114,3057,3657,3599,898,2950,1931,2270,1988,2043,1871,1817,2326,2104,2159,2216,530,125,173,1545,6366,6202,4715,5469,5524,4956,5020,5078,5138,5684,5304,5358,5416,5581,5631,5192,5250,2605,2553,4855,4809,4761,1592,777,2841,2897,6317,1283,6096,1332,6150,1053,859,965,4568,4523,4478,4433,4388"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,96,142,187,237,291,342,382,427,472,529,583,641,691,746,792,844,890,937,989,1038,1080,1127,1175,1215,1261,1314,1363,1422,1471,1523,1578,1632,1684,1738,1791,1846,1900,1956,2010,2066,2111,2162,2214,2267,2320,2375,2428,2482,2540,2594,2651,2710,2768,2824,2863,2916,2976,3030,3087,3142,3196,3252,3308,3369,3424,3481,3523,3570,3618,3680,3729,3781,3827,3880,3935,3997,4061,4119,4179,4232,4286,4340,4398,4455,4505,4559,4617,4669,4723,4769,4817,4863,4910,4952,5003,5059,5110,5157,5209,5258,5312,5360,5402,5442,5487,5532,5577,5622", "endColumns": "40,45,44,49,53,50,39,44,44,56,53,57,49,54,45,51,45,46,51,48,41,46,47,39,45,52,48,58,48,51,54,53,51,53,52,54,53,55,53,55,44,50,51,52,52,54,52,53,57,53,56,58,57,55,38,52,59,53,56,54,53,55,55,60,54,56,41,46,47,61,48,51,45,52,54,61,63,57,59,52,53,53,57,56,49,53,57,51,53,45,47,45,46,41,50,55,50,46,51,48,53,47,41,39,44,44,44,44,44", "endOffsets": "91,137,182,232,286,337,377,422,467,524,578,636,686,741,787,839,885,932,984,1033,1075,1122,1170,1210,1256,1309,1358,1417,1466,1518,1573,1627,1679,1733,1786,1841,1895,1951,2005,2061,2106,2157,2209,2262,2315,2370,2423,2477,2535,2589,2646,2705,2763,2819,2858,2911,2971,3025,3082,3137,3191,3247,3303,3364,3419,3476,3518,3565,3613,3675,3724,3776,3822,3875,3930,3992,4056,4114,4174,4227,4281,4335,4393,4450,4500,4554,4612,4664,4718,4764,4812,4858,4905,4947,4998,5054,5105,5152,5204,5253,5307,5355,5397,5437,5482,5527,5572,5617,5662"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6ae4eed1b1ae105f01d3e7330e2833b6\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,163,164,165,166,167,168,169,170", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7162,7237,7348,7437,7538,7645,7752,7851,7958,8061,8188,8276,8400,8502,8604,8720,8822,8936,9064,9180,9302,9438,9558,9692,9812,9924,10139,10256,10380,10510,10632,10770,10904,11020", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "7232,7343,7432,7533,7640,7747,7846,7953,8056,8183,8271,8395,8497,8599,8715,8817,8931,9059,9175,9297,9433,9553,9687,9807,9919,10045,10251,10375,10505,10627,10765,10899,11015,11135"}}, {"source": "C:\\xampp\\htdocs\\GoGoLaundry\\GoGoLaundryApp\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "100", "endLines": "19", "endColumns": "12", "endOffsets": "1090"}, "to": {"startLines": "111", "startColumns": "4", "startOffsets": "5667", "endLines": "128", "endColumns": "12", "endOffsets": "6525"}}]}]}